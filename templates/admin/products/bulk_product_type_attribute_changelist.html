{% extends "admin/change_list.html" %}
{% load i18n admin_urls static %}

{% block content_title %}
    <h1>Bulk Add Product Type Attributes</h1>
{% endblock %}

{% block extrahead %}{{ block.super }}
<style>
    .bulk-info-container {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 4px;
        padding: 15px;
        margin: 20px 0;
    }
    
    .bulk-info-container h3 {
        color: #0c5460;
        margin-top: 0;
    }
    
    .bulk-info-container p {
        margin-bottom: 10px;
        color: #0c5460;
    }
    
    .bulk-add-button {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
        padding: 12px 24px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        font-weight: bold;
        margin-top: 10px;
    }
    
    .bulk-add-button:hover {
        background-color: #218838;
        border-color: #1e7e34;
        color: white;
        text-decoration: none;
    }
    
    .regular-admin-link {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        margin-left: 10px;
    }
    
    .regular-admin-link:hover {
        background-color: #5a6268;
        border-color: #545b62;
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="bulk-info-container">
    <h3>Efficient Bulk Operations</h3>
    <p>This interface allows you to associate multiple attributes with a product type in a single operation, 
    making it much more efficient than adding them one by one.</p>
    
    <p><strong>How it works:</strong></p>
    <ul>
        <li>Select a product type from the dropdown</li>
        <li>Choose multiple attributes using the checkboxes</li>
        <li>Set global options (filterable, option selector) that apply to all selected attributes</li>
        <li>Save to create all associations at once</li>
    </ul>
    
    <a href="{% url 'admin:products_bulk_product_type_attribute_add' %}" class="bulk-add-button">
        ➕ Bulk Add Product Type Attributes
    </a>
    
    <a href="{% url 'admin:products_producttypeattributeproxy_changelist' %}" class="regular-admin-link">
        📋 View All Product Type Attributes
    </a>
</div>
{% endblock %}
