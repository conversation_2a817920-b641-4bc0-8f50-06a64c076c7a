{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}{{ block.super }}
<script src="{% url 'admin:jsi18n' %}"></script>
{{ media }}
<!-- Include jQuery and Select2 for enhanced dropdowns -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<style>
    .management-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .product-type-selection {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #007cba;
    }
    
    .product-type-selection h3 {
        margin-top: 0;
        color: #007cba;
    }
    
    .associations-container {
        margin-top: 30px;
    }
    
    .existing-associations {
        margin-bottom: 30px;
    }
    
    .existing-associations h4 {
        color: #28a745;
        border-bottom: 2px solid #28a745;
        padding-bottom: 10px;
    }
    
    .new-associations h4 {
        color: #17a2b8;
        border-bottom: 2px solid #17a2b8;
        padding-bottom: 10px;
    }
    
    .attribute-row {
        display: grid;
        grid-template-columns: 2fr auto auto auto;
        align-items: center;
        gap: 15px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background-color: #f9f9f9;
        transition: all 0.3s ease;
        min-height: 60px;
    }

    .attribute-row.saved {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .attribute-row.editing {
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }

    .attribute-row.error {
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .attribute-field {
        min-width: 250px;
    }

    .checkbox-field {
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
        min-width: 120px;
    }

    .checkbox-field label {
        font-size: 12px;
        margin: 0;
        cursor: pointer;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        min-width: 120px;
    }
    
    .btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s ease;
    }
    
    .btn-save {
        background-color: #28a745;
        color: white;
    }
    
    .btn-save:hover {
        background-color: #218838;
    }
    
    .btn-edit {
        background-color: #ffc107;
        color: #212529;
    }
    
    .btn-edit:hover {
        background-color: #e0a800;
    }
    
    .btn-delete {
        background-color: #dc3545;
        color: white;
    }
    
    .btn-delete:hover {
        background-color: #c82333;
    }
    
    .btn-cancel {
        background-color: #6c757d;
        color: white;
    }
    
    .btn-cancel:hover {
        background-color: #5a6268;
    }
    
    .btn-add-row {
        background-color: #17a2b8;
        color: white;
        padding: 10px 20px;
        margin-top: 15px;
    }
    
    .btn-add-row:hover {
        background-color: #138496;
    }
    
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
    
    .success-message, .error-message {
        padding: 10px 15px;
        margin: 10px 0;
        border-radius: 4px;
        font-weight: bold;
    }
    
    .success-message {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .hidden {
        display: none;
    }
    
    .attribute-name {
        font-weight: bold;
        color: #495057;
    }
    
    .no-associations {
        text-align: center;
        padding: 40px;
        color: #6c757d;
        font-style: italic;
    }
    
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background-color: white;
    }

    .form-check-input {
        margin: 0;
        transform: scale(1.2);
    }

    /* Select2 customization */
    .select2-container {
        width: 100% !important;
    }

    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 6px 12px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 24px;
        color: #495057;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }

    .select2-dropdown {
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #007cba;
    }

    /* Product type selection styling */
    .product-type-selection .form-control,
    .product-type-selection .select2-container {
        max-width: 400px;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .attribute-row {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .checkbox-field {
            justify-content: flex-start;
        }

        .action-buttons {
            justify-content: flex-start;
        }

        .management-container {
            margin: 10px;
            padding: 15px;
        }
    }

    /* Better spacing for form elements */
    .product-type-selection form > div {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    /* Loading state improvements */
    .btn.loading {
        position: relative;
        color: transparent;
    }

    .btn.loading::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
    }
    
    .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007cba;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="management-container">
    <h1>{{ title }}</h1>
    
    <div class="product-type-selection">
        <h3>📋 Select Product Type</h3>
        <p>Choose a product type to view and manage its attribute associations.</p>
        
        <form id="product-type-form">
            {% csrf_token %}
            <div style="display: flex; align-items: center; gap: 15px;">
                {{ product_type_form.product_type }}
                <button type="button" id="load-associations-btn" class="btn btn-save" disabled>
                    Load Attributes
                </button>
            </div>
            {% if product_type_form.product_type.help_text %}
                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                    {{ product_type_form.product_type.help_text }}
                </div>
            {% endif %}
        </form>
    </div>
    
    <div id="messages-container"></div>
    
    <div id="associations-container" class="associations-container hidden">
        <div class="existing-associations">
            <h4>🔗 Existing Associations</h4>
            <div id="existing-associations-list">
                <div class="no-associations">
                    No existing associations found.
                </div>
            </div>
        </div>
        
        <div class="new-associations">
            <h4>➕ Add New Associations</h4>
            <div id="new-associations-list">
                <!-- New association rows will be added here -->
            </div>
            <button type="button" id="add-new-row-btn" class="btn btn-add-row">
                + Add Another Attribute
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const productTypeSelect = document.getElementById('id_product_type_selector');
    const loadBtn = document.getElementById('load-associations-btn');
    const associationsContainer = document.getElementById('associations-container');
    const existingList = document.getElementById('existing-associations-list');
    const newList = document.getElementById('new-associations-list');
    const addRowBtn = document.getElementById('add-new-row-btn');
    const messagesContainer = document.getElementById('messages-container');

    let currentProductTypeId = null;
    let existingAttributeIds = [];

    // Initialize Select2 for product type selector
    $(productTypeSelect).select2({
        placeholder: 'Search and select a product type...',
        allowClear: true,
        width: '100%'
    });

    // Enable/disable load button based on selection (using Select2 events)
    $(productTypeSelect).on('change', function() {
        const hasValue = this.value && this.value !== '';
        loadBtn.disabled = !hasValue;
        if (!hasValue) {
            associationsContainer.classList.add('hidden');
            currentProductTypeId = null;
        }
        console.log('Product type changed:', this.value, 'Button disabled:', loadBtn.disabled);
    });
    
    // Load associations when button is clicked
    loadBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const productTypeId = productTypeSelect.value;
        console.log('Load button clicked, product type ID:', productTypeId);

        if (!productTypeId) {
            showMessage('Please select a product type first', 'error');
            return;
        }

        currentProductTypeId = productTypeId;
        loadExistingAssociations(productTypeId);
    });
    
    // Add new row button
    addRowBtn.addEventListener('click', function() {
        addNewAssociationRow();
    });
    
    function loadExistingAssociations(productTypeId) {
        console.log('Loading associations for product type:', productTypeId);
        showLoading(loadBtn);

        const url = `{% url 'admin:products_ajax_load_associations' %}?product_type_id=${productTypeId}`;
        console.log('Fetching URL:', url);

        fetch(url)
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                hideLoading(loadBtn);

                if (data.success) {
                    displayExistingAssociations(data.associations);
                    associationsContainer.classList.remove('hidden');

                    // Clear and add initial new rows
                    newList.innerHTML = '';
                    for (let i = 0; i < 3; i++) {
                        addNewAssociationRow();
                    }
                    showMessage(`Loaded ${data.associations.length} existing associations`, 'success');
                } else {
                    showMessage('Error loading associations: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                hideLoading(loadBtn);
                showMessage('Error: ' + error.message, 'error');
            });
    }
    
    function displayExistingAssociations(associations) {
        existingAttributeIds = associations.map(a => a.attribute_id);
        
        if (associations.length === 0) {
            existingList.innerHTML = '<div class="no-associations">No existing associations found.</div>';
            return;
        }
        
        existingList.innerHTML = '';
        associations.forEach(association => {
            const row = createExistingAssociationRow(association);
            existingList.appendChild(row);
        });
    }
    
    function createExistingAssociationRow(association) {
        const row = document.createElement('div');
        row.className = 'attribute-row saved';
        row.dataset.associationId = association.id;
        row.dataset.attributeId = association.attribute_id;
        
        row.innerHTML = `
            <div class="attribute-field">
                <span class="attribute-name">${association.attribute_name}</span>
            </div>
            <div class="checkbox-field">
                <input type="checkbox" ${association.is_filterable ? 'checked' : ''} disabled>
                <label>Is filterable</label>
            </div>
            <div class="checkbox-field">
                <input type="checkbox" ${association.is_option_selector ? 'checked' : ''} disabled>
                <label>Is option selector</label>
            </div>
            <div class="action-buttons">
                <button type="button" class="btn btn-edit" onclick="editAssociation(this)">Edit</button>
                <button type="button" class="btn btn-delete" onclick="deleteAssociation(this)">Delete</button>
            </div>
        `;
        
        return row;
    }
    
    function addNewAssociationRow() {
        const row = document.createElement('div');
        row.className = 'attribute-row';
        const timestamp = Date.now();

        row.innerHTML = `
            <div class="attribute-field">
                <select class="form-control attribute-select" required>
                    <option value="">Search and select an attribute...</option>
                </select>
            </div>
            <div class="checkbox-field">
                <input type="checkbox" class="form-check-input is-filterable" id="filterable_${timestamp}">
                <label for="filterable_${timestamp}">Is filterable</label>
            </div>
            <div class="checkbox-field">
                <input type="checkbox" class="form-check-input is-option-selector" id="option_${timestamp}">
                <label for="option_${timestamp}">Is option selector</label>
            </div>
            <div class="action-buttons">
                <button type="button" class="btn btn-save" onclick="saveNewAssociation(this)">Save</button>
            </div>
        `;

        newList.appendChild(row);

        // Load available attributes for this row and initialize Select2
        const selectElement = row.querySelector('.attribute-select');
        loadAvailableAttributes(selectElement);
    }
    
    function loadAvailableAttributes(selectElement) {
        if (!currentProductTypeId) return;

        fetch(`{% url 'admin:products_ajax_load_attributes' %}?product_type_id=${currentProductTypeId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    selectElement.innerHTML = '<option value="">Search and select an attribute...</option>';
                    data.attributes.forEach(attr => {
                        const option = document.createElement('option');
                        option.value = attr.id;
                        option.textContent = attr.title;
                        selectElement.appendChild(option);
                    });

                    // Initialize Select2 for this attribute selector
                    $(selectElement).select2({
                        placeholder: 'Search and select an attribute...',
                        allowClear: true,
                        width: '100%',
                        dropdownParent: $(selectElement).parent()
                    });
                } else {
                    console.error('Error loading attributes:', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }
    
    function showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
        messageDiv.textContent = message;
        
        messagesContainer.innerHTML = '';
        messagesContainer.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }
    
    function showLoading(button) {
        button.disabled = true;
        button.dataset.originalText = button.textContent;
        button.classList.add('loading');
    }

    function hideLoading(button) {
        button.disabled = false;
        button.classList.remove('loading');
        button.textContent = button.dataset.originalText || button.textContent;
    }
    
    // Global functions for button actions
    window.editAssociation = function(button) {
        const row = button.closest('.attribute-row');
        const checkboxes = row.querySelectorAll('input[type="checkbox"]');
        
        // Enable checkboxes
        checkboxes.forEach(cb => cb.disabled = false);
        
        // Change buttons
        const actionButtons = row.querySelector('.action-buttons');
        actionButtons.innerHTML = `
            <button type="button" class="btn btn-save" onclick="updateAssociation(this)">Update</button>
            <button type="button" class="btn btn-cancel" onclick="cancelEdit(this)">Cancel</button>
        `;
        
        row.classList.add('editing');
    };
    
    window.updateAssociation = function(button) {
        const row = button.closest('.attribute-row');
        const associationId = row.dataset.associationId;
        const isFilterable = row.querySelector('input[type="checkbox"]:nth-of-type(1)').checked;
        const isOptionSelector = row.querySelector('input[type="checkbox"]:nth-of-type(2)').checked;
        
        showLoading(button);
        
        fetch('{% url "admin:products_ajax_update_association" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                association_id: associationId,
                is_filterable: isFilterable,
                is_option_selector: isOptionSelector
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(button);
            
            if (data.success) {
                showMessage(data.message, 'success');
                
                // Disable checkboxes and restore buttons
                const checkboxes = row.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(cb => cb.disabled = true);
                
                const actionButtons = row.querySelector('.action-buttons');
                actionButtons.innerHTML = `
                    <button type="button" class="btn btn-edit" onclick="editAssociation(this)">Edit</button>
                    <button type="button" class="btn btn-delete" onclick="deleteAssociation(this)">Delete</button>
                `;
                
                row.classList.remove('editing');
                row.classList.add('saved');
            } else {
                showMessage('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            hideLoading(button);
            showMessage('Error: ' + error.message, 'error');
        });
    };
    
    window.cancelEdit = function(button) {
        // Reload the associations to restore original state
        if (currentProductTypeId) {
            loadExistingAssociations(currentProductTypeId);
        }
    };
    
    window.deleteAssociation = function(button) {
        if (!confirm('Are you sure you want to remove this attribute association?')) {
            return;
        }
        
        const row = button.closest('.attribute-row');
        const associationId = row.dataset.associationId;
        
        showLoading(button);
        
        fetch('{% url "admin:products_ajax_delete_association" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                association_id: associationId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');

                // Clean up Select2 if it exists
                const select2Element = row.querySelector('.select2-container');
                if (select2Element) {
                    $(row.querySelector('.attribute-select')).select2('destroy');
                }

                row.remove();

                // Remove from existing attribute IDs
                const attributeId = parseInt(row.dataset.attributeId);
                existingAttributeIds = existingAttributeIds.filter(id => id !== attributeId);
            } else {
                hideLoading(button);
                showMessage('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            hideLoading(button);
            showMessage('Error: ' + error.message, 'error');
        });
    };
    
    window.saveNewAssociation = function(button) {
        const row = button.closest('.attribute-row');
        const attributeSelect = row.querySelector('.attribute-select');
        const isFilterable = row.querySelector('.is-filterable').checked;
        const isOptionSelector = row.querySelector('.is-option-selector').checked;
        
        if (!attributeSelect.value) {
            showMessage('Please select an attribute', 'error');
            return;
        }
        
        showLoading(button);
        
        fetch('{% url "admin:products_ajax_save_association" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_type_id: currentProductTypeId,
                attribute_id: attributeSelect.value,
                is_filterable: isFilterable,
                is_option_selector: isOptionSelector
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(button);
            
            if (data.success) {
                showMessage(data.message, 'success');
                
                // Move this row to existing associations
                const association = {
                    id: data.association_id,
                    attribute_id: parseInt(attributeSelect.value),
                    attribute_name: data.attribute_name,
                    is_filterable: isFilterable,
                    is_option_selector: isOptionSelector
                };
                
                const existingRow = createExistingAssociationRow(association);
                existingList.appendChild(existingRow);
                
                // Remove the "no associations" message if it exists
                const noAssociations = existingList.querySelector('.no-associations');
                if (noAssociations) {
                    noAssociations.remove();
                }
                
                // Remove this row from new associations
                row.remove();
                
                // Add to existing attribute IDs
                existingAttributeIds.push(association.attribute_id);
                
                // Add a new empty row
                addNewAssociationRow();
            } else {
                showMessage('Error: ' + data.error, 'error');
                row.classList.add('error');
                setTimeout(() => row.classList.remove('error'), 3000);
            }
        })
        .catch(error => {
            hideLoading(button);
            showMessage('Error: ' + error.message, 'error');
        });
    };
});
</script>
{% endblock %}
