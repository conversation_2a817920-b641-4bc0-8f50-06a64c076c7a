from django import forms
from .models import ProductVariant, AttributeValue, ProductType, Attribute, Category, ProductImage, ProductTypeAttribute


class ProductVariantForm(forms.ModelForm):
    class Meta:
        model = ProductVariant
        fields = '__all__'  # Include all fields from the ProductVariant model in the form

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Check if this is a new instance (creation)
        if not self.instance.pk:
            # Disable the price_label field for new instances
            self.fields['price_label'].disabled = True
            self.fields['price_label'].required = False
            self.fields[
                'price_label'].help_text = "You can associate an attribute value with the price label after saving."
        else:
            # For existing instances, enable the field and update the queryset
            self.fields['price_label'].disabled = False
            self.fields['price_label'].required = False
            product = self.instance.product
            product_type = product.product_type
            self.fields['price_label'].queryset = AttributeValue.objects.filter(
                attribute__product_type_attribute_a__product_type=product_type
            )
            self.fields['price_label'].help_text = "Select an attribute value to associate with this price label."

    def clean(self):
        cleaned_data = super().clean()
        # If this is a new instance, remove price_label from cleaned_data
        if not self.instance.pk:
            cleaned_data.pop('price_label', None)
        return cleaned_data


class ProductTypeForm(forms.ModelForm):
    class Meta:
        model = ProductType
        fields = ['title', 'parent']  # Add other fields if needed
        widgets = {
            'title': forms.TextInput(attrs={
                'placeholder': 'e.g., hard-drive',  # Shadow text inside input field
                # 'class': 'product-type-title',  # You can add custom CSS classes if needed
            }),
            'parent': forms.Select(attrs={
                'class': 'custom-select-class',
            }),
        }
        help_texts = {
            'title': 'e.g., hard-drive (make it singular and lowercase)',
            # 'parent': 'Select a parent product type if applicable.',
        }


class AttributeForm(forms.ModelForm):
    class Meta:
        model = Attribute
        fields = ['title']  # Define fields you want to include in the form
        widgets = {
            'title': forms.TextInput(attrs={
                'placeholder': 'e.g., Storage Capacity',  # Placeholder text
            }),
        }


class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['title', 'parent', 'slug']
        widgets = {
            'title': forms.TextInput(attrs={
                'placeholder': 'e.g., Hard Drives',
            })
        }
        help_texts = {
            'title': 'e.g., Hard Drives, CPUs (make it Capitalized or Uppercase and plural exactly as you want to '
                     'show it in the frontend)',
            'slug': 'This is an auto-generated field. Modify with caution if necessary.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Disable slug field if this is a creation form (instance does not exist yet)
        if not self.instance.pk:
            self.fields['slug'].widget = forms.HiddenInput()


class ProductImageForm(forms.ModelForm):
    class Meta:
        model = ProductImage
        fields = ['alternative_text', 'image', 'product_variant']
        help_texts = {
            'alternative_text': 'A short description of the image for accessibility',
            'image': 'Upload an image file',
        }
        widgets = {
            'product_variant': forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make the order field not required as it's auto-generated
        if 'order' in self.fields:
            self.fields['order'].required = False

        # Make product_variant not required in the form (it will be set automatically)
        if 'product_variant' in self.fields:
            self.fields['product_variant'].required = False

    def clean(self):
        cleaned_data = super().clean()
        # Ensure we have the required fields
        if not cleaned_data.get('alternative_text'):
            self.add_error('alternative_text', 'Please provide alternative text for the image')
        if not cleaned_data.get('image') and not self.instance.pk:
            self.add_error('image', 'Please select an image to upload')
        return cleaned_data


class BulkProductTypeAttributeForm(forms.Form):
    """
    Form for bulk associating multiple attributes with a single product type.
    This allows admins to select one product type and multiple attributes
    to create multiple ProductTypeAttribute relationships at once.
    """
    product_type = forms.ModelChoiceField(
        queryset=ProductType.objects.all(),
        empty_label="Select a Product Type",
        widget=forms.Select(attrs={
            'class': 'form-control',
            'style': 'width: 300px;'
        }),
        help_text="Choose the product type to associate attributes with"
    )

    attributes = forms.ModelMultipleChoiceField(
        queryset=Attribute.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'attribute-checkbox-list'
        }),
        help_text="Select one or more attributes to associate with the product type"
    )

    # Global settings that apply to all selected attributes
    is_filterable = forms.BooleanField(
        required=False,
        initial=False,
        help_text="Make all selected attributes filterable"
    )

    is_option_selector = forms.BooleanField(
        required=False,
        initial=False,
        help_text="Make all selected attributes option selectors"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # If we have a product_type in initial data, filter out already associated attributes
        if 'initial' in kwargs and 'product_type' in kwargs['initial']:
            product_type = kwargs['initial']['product_type']
            if product_type:
                # Get attributes already associated with this product type
                existing_attributes = Attribute.objects.filter(
                    product_type_attribute_a__product_type=product_type
                )
                # Show only attributes not yet associated
                self.fields['attributes'].queryset = Attribute.objects.exclude(
                    id__in=existing_attributes.values_list('id', flat=True)
                )

    def clean(self):
        cleaned_data = super().clean()
        product_type = cleaned_data.get('product_type')
        attributes = cleaned_data.get('attributes')

        if product_type and attributes:
            # Check for any attributes that are already associated with this product type
            existing_associations = ProductTypeAttribute.objects.filter(
                product_type=product_type,
                attribute__in=attributes
            ).values_list('attribute__title', flat=True)

            if existing_associations:
                existing_list = ', '.join(existing_associations)
                raise forms.ValidationError(
                    f"The following attributes are already associated with {product_type.title}: {existing_list}"
                )

        return cleaned_data
