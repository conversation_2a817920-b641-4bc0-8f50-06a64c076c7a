from django import forms
from .models import ProductVariant, AttributeValue, ProductType, Attribute, Category, ProductImage, ProductTypeAttribute


class ProductVariantForm(forms.ModelForm):
    class Meta:
        model = ProductVariant
        fields = '__all__'  # Include all fields from the ProductVariant model in the form

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Check if this is a new instance (creation)
        if not self.instance.pk:
            # Disable the price_label field for new instances
            self.fields['price_label'].disabled = True
            self.fields['price_label'].required = False
            self.fields[
                'price_label'].help_text = "You can associate an attribute value with the price label after saving."
        else:
            # For existing instances, enable the field and update the queryset
            self.fields['price_label'].disabled = False
            self.fields['price_label'].required = False
            product = self.instance.product
            product_type = product.product_type
            self.fields['price_label'].queryset = AttributeValue.objects.filter(
                attribute__product_type_attribute_a__product_type=product_type
            )
            self.fields['price_label'].help_text = "Select an attribute value to associate with this price label."

    def clean(self):
        cleaned_data = super().clean()
        # If this is a new instance, remove price_label from cleaned_data
        if not self.instance.pk:
            cleaned_data.pop('price_label', None)
        return cleaned_data


class ProductTypeForm(forms.ModelForm):
    class Meta:
        model = ProductType
        fields = ['title', 'parent']  # Add other fields if needed
        widgets = {
            'title': forms.TextInput(attrs={
                'placeholder': 'e.g., hard-drive',  # Shadow text inside input field
                # 'class': 'product-type-title',  # You can add custom CSS classes if needed
            }),
            'parent': forms.Select(attrs={
                'class': 'custom-select-class',
            }),
        }
        help_texts = {
            'title': 'e.g., hard-drive (make it singular and lowercase)',
            # 'parent': 'Select a parent product type if applicable.',
        }


class AttributeForm(forms.ModelForm):
    class Meta:
        model = Attribute
        fields = ['title']  # Define fields you want to include in the form
        widgets = {
            'title': forms.TextInput(attrs={
                'placeholder': 'e.g., Storage Capacity',  # Placeholder text
            }),
        }


class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['title', 'parent', 'slug']
        widgets = {
            'title': forms.TextInput(attrs={
                'placeholder': 'e.g., Hard Drives',
            })
        }
        help_texts = {
            'title': 'e.g., Hard Drives, CPUs (make it Capitalized or Uppercase and plural exactly as you want to '
                     'show it in the frontend)',
            'slug': 'This is an auto-generated field. Modify with caution if necessary.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Disable slug field if this is a creation form (instance does not exist yet)
        if not self.instance.pk:
            self.fields['slug'].widget = forms.HiddenInput()


class ProductImageForm(forms.ModelForm):
    class Meta:
        model = ProductImage
        fields = ['alternative_text', 'image', 'product_variant']
        help_texts = {
            'alternative_text': 'A short description of the image for accessibility',
            'image': 'Upload an image file',
        }
        widgets = {
            'product_variant': forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make the order field not required as it's auto-generated
        if 'order' in self.fields:
            self.fields['order'].required = False

        # Make product_variant not required in the form (it will be set automatically)
        if 'product_variant' in self.fields:
            self.fields['product_variant'].required = False

    def clean(self):
        cleaned_data = super().clean()
        # Ensure we have the required fields
        if not cleaned_data.get('alternative_text'):
            self.add_error('alternative_text', 'Please provide alternative text for the image')
        if not cleaned_data.get('image') and not self.instance.pk:
            self.add_error('image', 'Please select an image to upload')
        return cleaned_data


class ProductTypeSelectionForm(forms.Form):
    """
    Simple form for selecting a product type to manage its attributes.
    """
    product_type = forms.ModelChoiceField(
        queryset=ProductType.objects.all(),
        empty_label="Select a Product Type",
        widget=forms.Select(attrs={
            'class': 'form-control product-type-selector',
            'id': 'id_product_type_selector'
        }),
        help_text="Choose the product type to manage attributes for"
    )


class ProductTypeAttributeRowForm(forms.Form):
    """
    Form for individual product type attribute association.
    Used for both creating new associations and editing existing ones.
    """
    attribute = forms.ModelChoiceField(
        queryset=Attribute.objects.all(),
        empty_label="Search and select an attribute...",
        widget=forms.Select(attrs={
            'class': 'form-control attribute-autocomplete',
            'data-placeholder': 'Search for an attribute...'
        }),
        help_text="Search and select an attribute to associate"
    )

    is_filterable = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text="Make this attribute filterable"
    )

    is_option_selector = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text="Make this attribute an option selector"
    )

    # Hidden field to track if this is an existing association
    association_id = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput()
    )

    def __init__(self, *args, **kwargs):
        product_type = kwargs.pop('product_type', None)
        exclude_attributes = kwargs.pop('exclude_attributes', [])
        super().__init__(*args, **kwargs)

        if product_type:
            # Filter out attributes already associated with this product type
            # (except for the current one if editing)
            current_attribute_id = self.initial.get('attribute')
            exclude_list = list(exclude_attributes)
            if current_attribute_id:
                exclude_list = [attr_id for attr_id in exclude_list if attr_id != current_attribute_id]

            self.fields['attribute'].queryset = Attribute.objects.exclude(
                id__in=exclude_list
            ).order_by('title')

    def clean(self):
        cleaned_data = super().clean()
        attribute = cleaned_data.get('attribute')

        if not attribute:
            raise forms.ValidationError("Please select an attribute.")

        return cleaned_data
